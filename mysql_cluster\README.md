# MySQL 1主2从数据库集群部署指南

本项目使用 Docker Compose 部署一个包含一个主节点（Master）和两个从节点（Slave）的 MySQL 8.0 数据库集群，并实现了主从复制的自动初始化。

## 目录结构

```
mysql_cluster/
├── compose.yaml          # Docker Compose 配置文件，定义了三个MySQL服务
├── conf/                 # 存放 MySQL 的配置文件
│   ├── master.cnf        # 主节点的配置文件
│   └── slave.cnf         # 从节点的共享配置文件
├── init/                 # 存放初始化脚本
│   ├── master.sql        # 主节点初始化时执行，用于创建复制用户
│   └── slave.sh          # 从节点启动时执行，用于自动配置和启动复制
└── README.md             # 本部署指南
```

## 部署步骤

1.  **上传文件**:
    将整个 `mysql_cluster` 目录上传到您的 Rocky Linux 服务器。

2.  **进入目录**:
    通过 `cd` 命令进入服务器上的 `mysql_cluster` 目录。
    ```bash
    cd /path/to/your/mysql_cluster
    ```

3.  **启动集群**:
    在 `mysql_cluster` 目录下，执行以下命令以后台模式启动并构建所有服务。1
    ```bash
    docker-compose up -d --build
    ```

## 验证与监控

*   **查看服务状态**:
    使用以下命令检查所有容器是否都已正常启动并处于 `Up` 或 `healthy` 状态。
    ```bash
    docker-compose ps
    ```

*   **查看实时日志**:
    通过此命令可以监控所有容器的启动过程和实时日志，特别是在首次启动时，您可以观察到主从复制的自动配置过程。
    ```bash
    docker-compose logs -f
    ```
    您可以重点关注从节点日志中 `SHOW SLAVE STATUS` 的输出，确认 `Slave_IO_Running` 和 `Slave_SQL_Running` 的值都为 `Yes`。

*   **停止集群**:
    如果需要停止并移除所有容器、网络和数据卷，可以执行以下命令：
    ```bash
    docker-compose down -v
    ```
    **注意**: `-v` 参数会删除所有通过 `volumes` 创建的数据卷，包括数据库文件。如果希望保留数据，请不要使用 `-v` 参数。

## 自动化说明

- **主节点 (mysql_master)**: 启动时，会自动执行 `init/master.sql` 脚本，创建一个名为 `replicator` 的用户，并授予其复制所需的权限。
- **从节点 (mysql_slave1, mysql_slave2)**: 在主节点健康检查通过后才会启动。启动时，它们会执行 `init/slave.sh` 脚本，该脚本会：
  1.  等待主节点网络可用。
  2.  使用 GTID 模式自动配置与主节点的复制关系。
  3.  启动复制进程。

## 如何扩容从节点

得益于 Docker Compose 的声明式配置，为集群增加更多的从节点非常简单。假设您需要将从节点数量从 2 个增加到 4 个，请按照以下步骤操作：

### 1. 修改 `compose.yaml` 文件

打开 `compose.yaml` 文件，在 `services` 部分的末尾添加新的从节点服务。您可以直接复制一个已有的从节点配置（如 `mysql_slave2`）并修改以下关键参数：

- **服务名称**: 为新服务起一个唯一的名字（例如 `mysql_slave3`）。
- **`container_name`**: 更改为唯一的容器名称（例如 `mysql_slave3`）。
- **`command` -> `--server-id`**: **此项至关重要**。每个节点的 `server-id` 必须是集群中唯一的。例如，新节点应使用 `4`、`5` 等。
- **`volumes`**: 为新节点的数据持久化指定一个新的目录，以避免数据冲突（例如 `./data/slave3:/var/lib/mysql`）。

**示例：添加 `mysql_slave3`**

```yaml
  mysql_slave3:
    image: mysql:8.0
    container_name: mysql_slave3
    restart: always
    depends_on:
      mysql_master:
        condition: service_healthy
    environment:
      MYSQL_ROOT_PASSWORD: "root_password"
    volumes:
      - ./data/slave3:/var/lib/mysql  # <-- 新的数据卷路径
      - ./conf/slave.cnf:/etc/mysql/conf.d/slave.cnf
      - ./init/slave.sh:/docker-entrypoint-initdb.d/slave.sh
    command:
      --server-id=4                   # <-- 新的、唯一的 server-id
      --relay-log=mysql-relay-bin
      --log-slave-updates=1
      --read-only=1
      --gtid-mode=ON
      --enforce-gtid-consistency=ON
    networks:
      - mysql_net
```
您可以按此模式添加任意数量的从节点 (`mysql_slave4`, `mysql_slave5`, ...)，只需确保 `server-id` 和数据卷路径是唯一的即可。

### 2. 应用更改

修改并保存 `compose.yaml` 文件后，在 `mysql_cluster` 目录下执行以下命令：

```bash
docker-compose up -d --build
```

Docker Compose 会自动创建并启动新添加的从节点容器。由于共享了 `init/slave.sh` 脚本，新的从节点会自动完成与主节点的复制配置，实现无缝扩容。

## 启动与执行顺序

为了确保集群能够全自动、可靠地完成初始化，系统采用了两个层面的机制来严格控制服务的启动顺序：

### 1. Docker Compose 依赖控制

这是第一层保证，通过 `compose.yaml` 文件中的 `healthcheck` 和 `depends_on` 指令实现：

- **主节点健康检查 (`healthcheck`)**: `mysql_master` 服务配置了健康检查，它会持续检查节点内部的 MySQL 服务是否已成功启动并能响应连接请求。
- **从节点启动依赖 (`depends_on`)**: 每个从节点服务都配置了 `condition: service_healthy`，这明确指示 Docker Compose **必须**等待 `mysql_master` 的健康检查成功后，才能启动从节点容器。

### 2. 初始化脚本保护机制

这是第二层保证，通过从节点内部执行的 `init/slave.sh` 脚本实现：

- **连接等待循环**: 在从节点容器启动后，脚本并不会立即执行复制配置命令。相反，它会进入一个循环，持续尝试通过网络连接到 `mysql_master`。
- **确保网络就绪**: 只有当从节点能够真正通过网络成功 `ping` 通主节点时，这个循环才会结束，脚本才会继续执行后续的 `CHANGE MASTER` 等关键命令。

### 最终执行流程

结合以上两点，整个集群的启动流程如下：

1.  **启动 `mysql_master` 容器**。
2.  **等待 `mysql_master` 健康检查通过**（意味着其内部 MySQL 服务已完全就绪）。
3.  **启动 `mysql_slave1` 和 `mysql_slave2` 容器**。
4.  从节点容器内的 `slave.sh` 脚本开始运行，并**等待网络能够成功连接到 `mysql_master`**。
5.  连接成功后，脚本**自动执行主从复制配置命令** (`CHANGE MASTER`, `START SLAVE`)。

通过这种双重保险机制，我们确保了主从关系总是在正确的时机、按正确的顺序建立，从而实现了可靠的自动化部署。 