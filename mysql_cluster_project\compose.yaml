services:
  mysql-master:
    image: 'rockylinux/rockylinux:9'
    container_name: mysql-master
    command: >
      bash -c "
        dnf install -y mysql-server &&
        /usr/sbin/mysqld --initialize-insecure --user=mysql &&
        /usr/sbin/mysqld --user=mysql --server-id=1 --log-bin=mysql-bin --enforce-gtid-consistency=ON --log-slave-updates=ON --gtid-mode=ON &
        sleep 30 &&
        mysql -e 'CREATE USER 'repl'@'%' IDENTIFIED BY 'password';' &&
        mysql -e 'GRANT REPLICATION SLAVE ON *.* TO 'repl'@'%';' &&
        mysql -e 'FLUSH PRIVILEGES;' &&
        tail -f /dev/null
      "
    volumes:
      - ./master:/var/lib/mysql
    networks:
      - mysql-cluster-network
    environment:
      - MYSQL_ROOT_PASSWORD=password

  mysql-slave:
    image: 'rockylinux/rockylinux:9'
    container_name: mysql-slave
    command: >
      bash -c "
        dnf install -y mysql-server &&
        /usr/sbin/mysqld --initialize-insecure --user=mysql &&
        /usr/sbin/mysqld --user=mysql --server-id=2 --log-bin=mysql-bin --enforce-gtid-consistency=ON --log-slave-updates=ON --gtid-mode=ON &
        sleep 45 &&
        mysql -e 'CHANGE MASTER TO MASTER_HOST='mysql-master',MASTER_USER='repl',MASTER_PASSWORD='password',MASTER_AUTO_POSITION=1;' &&
        mysql -e 'START SLAVE;' &&
        tail -f /dev/null
      "
    depends_on:
      - mysql-master
    volumes:
      - ./slave:/var/lib/mysql
    networks:
      - mysql-cluster-network
    environment:
      - MYSQL_ROOT_PASSWORD=password

  adminer:
    image: adminer
    container_name: adminer
    restart: always
    ports:
      - '8080:8080'
    networks:
      - mysql-cluster-network

networks:
  mysql-cluster-network:
    driver: bridge

volumes:
  mysql_master_data:
  mysql_slave_data: 