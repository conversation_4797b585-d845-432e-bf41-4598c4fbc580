services:
  mysql_master:
    image: mysql:8.0 # 使用 mysql 8.0 镜像
    container_name: mysql_master # 容器名称
    restart: always # 容器退出时总是重启
    environment: # 环境变量
      MYSQL_ROOT_PASSWORD: "root_password" # 设置 root 用户的密码
      MYSQL_DATABASE: "mydatabase" # 创建一个名为 mydatabase 的数据库
      MYSQL_USER: "myuser" # 创建一个普通用户
      MYSQL_PASSWORD: "mypassword" # 普通用户的密码
    ports:
      - "3306:3306" # 将主机的 3306 端口映射到容器的 3306 端口
    volumes:
      - ./data/master:/var/lib/mysql # 将主机的 ./data/master 目录挂载到容器的 /var/lib/mysql 目录，用于数据持久化
      - ./conf/master.cnf:/etc/mysql/conf.d/master.cnf # 挂载主节点配置文件
      - ./init/master.sql:/docker-entrypoint-initdb.d/master.sql # 挂载主节点初始化脚本
    command: # 容器启动时执行的命令
      --server-id=1 # 设置 master 的 server-id，必须唯一
      --log-bin=mysql-bin # 开启二进制日志，用于复制
      --binlog-do-db=mydatabase # 指定需要记录二进制日志的数据库
      --gtid-mode=ON # 开启 GTID 模式
      --enforce-gtid-consistency=ON # 强制 GTID 一致性
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$MYSQL_ROOT_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks: # 定义容器所连接的网络
      - mysql_net

  mysql_slave1:
    image: mysql:8.0 # 使用 mysql 8.0 镜像
    container_name: mysql_slave1 # 容器名称
    restart: always # 容器退出时总是重启
    depends_on:
      mysql_master:
        condition: service_healthy # 依赖于 mysql_master 服务的健康状态
    environment: # 环境变量 
      MYSQL_ROOT_PASSWORD: "root_password" # 设置 root 用户的密码
    volumes:
      - ./data/slave1:/var/lib/mysql # 数据持久化
      - ./conf/slave.cnf:/etc/mysql/conf.d/slave.cnf # 挂载从节点配置文件
      - ./init/slave.sh:/docker-entrypoint-initdb.d/slave.sh # 挂载从节点初始化脚本
    command: # 容器启动时执行的命令
      --server-id=2 # 设置 slave1 的 server-id，必须唯一
      --relay-log=mysql-relay-bin # 定义中继日志的名称
      --log-slave-updates=1 # 将从库的更新也记录到二进制日志中
      --read-only=1 # 设置为只读模式
      --gtid-mode=ON # 开启 GTID 模式
      --enforce-gtid-consistency=ON # 强制 GTID 一致性
    networks: # 定义容器所连接的网络
      - mysql_net

  mysql_slave2:
    image: mysql:8.0 # 使用 mysql 8.0 镜像
    container_name: mysql_slave2 # 容器名称
    restart: always # 容器退出时总是重启
    depends_on:
      mysql_master:
        condition: service_healthy # 依赖于 mysql_master 服务的健康状态
    environment: # 环境变量
      MYSQL_ROOT_PASSWORD: "root_password" # 设置 root 用户的密码
    volumes:
      - ./data/slave2:/var/lib/mysql # 数据持久化
      - ./conf/slave.cnf:/etc/mysql/conf.d/slave.cnf # 挂载从节点配置文件
      - ./init/slave.sh:/docker-entrypoint-initdb.d/slave.sh # 挂载从节点初始化脚本
    command: # 容器启动时执行的命令
      --server-id=3 # 设置 slave2 的 server-id，必须唯一
      --relay-log=mysql-relay-bin # 定义中继日志的名称
      --log-slave-updates=1 # 将从库的更新也记录到二进制日志中
      --read-only=1 # 设置为只读模式
      --gtid-mode=ON # 开启 GTID 模式
      --enforce-gtid-consistency=ON # 强制 GTID 一致性
    networks: # 定义容器所连接的网络
      - mysql_net

networks: # 定义网络
  mysql_net:
    driver: bridge # 使用桥接模式 