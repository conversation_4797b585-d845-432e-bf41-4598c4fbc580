# 使用官方推荐的 Compose 文件版本
version: '3.8'

# 'services' 是一个顶级关键字，下面定义了我们应用的所有服务（容器）
services:
  # 'db' 是我们为 MySQL 服务取的名字。在服务网络中，它也可以作为主机名使用
  db:
    # 指定要使用的 Docker 镜像。这里我们用官方的 MySQL 8.0 版本
    image: mysql:8.0

    # 'container_name' 是一个可选项，可以给容器一个固定的、友好的名字
    container_name: mysql_db_instance

    # 'restart: always' 是一个非常重要的生产实践。
    # 它告诉 Docker，无论何种原因导致容器停止，都要自动重启它，保证服务高可用
    restart: always

    # 'environment' 用来设置环境变量，这对于配置 MySQL 至关重要
    environment:
      MYSQL_ROOT_PASSWORD: root258369  # 请务必修改成一个更健壮的 root 用户密码
      MYSQL_DATABASE: my_app_db                     # Docker 会自动用这个名字创建一个数据库
      MYSQL_USER: my_app_user                       # Docker 会自动创建一个具有此用户名的用户
      MYSQL_PASSWORD: root258369     # 请务必修改成一个更健just的用户密码

    # 'ports' 用于端口映射，格式为 "主机端口:容器端口"
    ports:
      # 我们将主机的 3307 端口映射到容器内部的 3306 端口。
      # 这样做的好处是避免与您本地可能已安装的 MySQL（默认使用3306）产生端口冲突
      - "3307:3306"

    # 'volumes' 用于数据持久化。这是保证数据库数据不会丢失的关键！
    volumes:
      # 我们定义了一个名为 'mysql-data' 的具名卷 (named volume)，
      # 并将它挂载到容器内 MySQL 存储数据的标准路径 '/var/lib/mysql'。
      # Compose 会自动创建和管理这个卷。
      - mysql-data:/var/lib/mysql

# 在文件的顶层定义 'volumes'，以便 Compose 知道 'mysql-data' 是一个具名卷
volumes:
  mysql-data:
    # driver: local (这是默认值，可以不写) 